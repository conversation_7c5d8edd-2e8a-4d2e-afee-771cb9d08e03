FROM alpine:latest

ARG PB_VERSION=0.28.3

RUN apk add --no-cache \
    unzip \
    ca-certificates \
    openssh

# download and unzip PocketBase
ADD https://github.com/pocketbase/pocketbase/releases/download/v${PB_VERSION}/pocketbase_${PB_VERSION}_linux_amd64.zip /tmp/pb.zip

# Add debugging steps
RUN ls -la /tmp && \
    unzip /tmp/pb.zip -d /pb && \
    ls -la /pb && \
    chmod +x /pb/pocketbase && \
    ls -la /pb/pocketbase && \
    rm /tmp/pb.zip

# create superuser account automatically
RUN /pb/pocketbase <NAME_EMAIL> Sup3rGr3ml1n
# copy the local pb_hooks dir into the container
COPY ./pb_hooks /pb/pb_hooks

EXPOSE 8090

# start PocketBase
CMD ["/pb/pocketbase", "serve", "--http=0.0.0.0:8090", "--dir=/pb/pb_data"]

# On railway, replace 0.0.0.0:8090 with public domain for superuser account creation 
